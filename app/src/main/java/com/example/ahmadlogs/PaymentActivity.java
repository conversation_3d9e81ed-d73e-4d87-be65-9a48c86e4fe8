package com.example.ahmadlogs;

import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Base64;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;

import com.example.ahmadlogs.MainActivity;
import com.example.ahmadlogs.R;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class PaymentActivity extends AppCompatActivity {
    private static final String TAG = "JazzCashPayment";

    String postData = "";
    private WebView mWebView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private String currentPrice;

    private final String Jazz_MerchantID      = "MC158532";
    private final String Jazz_Password        = "0b64sxf81t";
    private final String Jazz_IntegritySalt   = "8zzx29y512";

    private static final String paymentReturnUrl="https://app-api.eswap.com/swap/pay/jazzcash/notify";
    private static final String SANDBOX_URL = "https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform/";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment);

        // 初始化SwipeRefreshLayout和WebView
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        mWebView = (WebView) findViewById(R.id.activity_payment_webview);

        // 设置下拉刷新监听器
        swipeRefreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                Log.d(TAG, "=== 用户触发下拉刷新，重新执行支付请求 ===");
                Toast.makeText(PaymentActivity.this, "重新发起支付请求...", Toast.LENGTH_SHORT).show();
                executePaymentRequest();
                swipeRefreshLayout.setRefreshing(false);
            }
        });

        // Enable Javascript
        WebSettings webSettings = mWebView.getSettings();
        webSettings.setJavaScriptEnabled(true);

        mWebView.setWebViewClient(new MyWebViewClient());
        webSettings.setDomStorageEnabled(true);
        mWebView.addJavascriptInterface(new FormDataInterface(), "FORMOUT");

        Intent intentData = getIntent();
        currentPrice = intentData.getStringExtra("price");
        Log.d(TAG, "=== 开始JazzCash支付流程 ===");
        Log.d(TAG, "原始价格: " + currentPrice);
        System.out.println("AhmadLogs: price_before : " + currentPrice);

        // 执行首次支付请求
        executePaymentRequest();
    }

    private void executePaymentRequest() {
        Log.d(TAG, "=== 执行支付请求 ===");
        postData = ""; // 重置postData

        String price = currentPrice;
        String[] values = price.split("\\.");
        price = values[0];
        price = price + "00";
        Log.d(TAG, "处理后的价格: " + price);
        System.out.println("AhmadLogs: price : " + price);

        Date Date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddkkmmss");
        String DateString = dateFormat.format(Date);
        Log.d(TAG, "交易日期时间: " + DateString);
        System.out.println("AhmadLogs: DateString : " + DateString);

        // Convert Date to Calendar
        Calendar c = Calendar.getInstance();
        c.setTime(Date);
        c.add(Calendar.HOUR, 5);

        // Convert calendar back to Date
        Date currentDateHourPlusOne = c.getTime();
        String expiryDateString = dateFormat.format(currentDateHourPlusOne);
        Log.d(TAG, "交易过期时间: " + expiryDateString);
        System.out.println("AhmadLogs: expiryDateString : " + expiryDateString);

        String TransactionIdString = "T" + DateString;
        Log.d(TAG, "交易ID: " + TransactionIdString);
        System.out.println("AhmadLogs: TransactionIdString : " + TransactionIdString);

        // 设置支付参数
        String pp_MerchantID = Jazz_MerchantID;
        String pp_Password = Jazz_Password;
        String IntegritySalt = Jazz_IntegritySalt;
        String pp_ReturnURL = paymentReturnUrl;
        String pp_Amount = price;
        String pp_TxnDateTime = DateString;
        String pp_TxnExpiryDateTime = expiryDateString;
        String pp_TxnRefNo = TransactionIdString;
        String pp_Version = "1.1";
        String pp_TxnType = "MPAY"; // 修复：设置为MPAY用于卡支付
        String pp_Language = "EN";
        String pp_SubMerchantID = "";
        String pp_BankID = "TBANK";
        String pp_ProductID = "RETL";
        String pp_TxnCurrency = "PKR";
        String pp_BillReference = "billRef";
        String pp_Description = "Description of transaction";
        String pp_SecureHash = "";
        String pp_mpf_1 = "1";
        String pp_mpf_2 = "2";
        String pp_mpf_3 = "3";
        String pp_mpf_4 = "4";
        String pp_mpf_5 = "5";

        // 打印所有参数用于调试
        Log.d(TAG, "=== 支付参数详情 ===");
        Log.d(TAG, "pp_MerchantID: " + pp_MerchantID);
        Log.d(TAG, "pp_Password: " + pp_Password);
        Log.d(TAG, "pp_Amount: " + pp_Amount);
        Log.d(TAG, "pp_TxnType: " + pp_TxnType);
        Log.d(TAG, "pp_TxnRefNo: " + pp_TxnRefNo);
        Log.d(TAG, "pp_TxnDateTime: " + pp_TxnDateTime);
        Log.d(TAG, "pp_TxnExpiryDateTime: " + pp_TxnExpiryDateTime);
        Log.d(TAG, "pp_ReturnURL: " + pp_ReturnURL);
        Log.d(TAG, "pp_BankID: " + pp_BankID);
        Log.d(TAG, "pp_ProductID: " + pp_ProductID);
        Log.d(TAG, "IntegritySalt: " + IntegritySalt);

        // 创建参数映射用于字母排序
        TreeMap<String, String> paramMap = new TreeMap<>();

        // 只添加非空参数（按照JazzCash文档要求）
        paramMap.put("pp_Amount", pp_Amount);
        paramMap.put("pp_BankID", pp_BankID);
        paramMap.put("pp_BillReference", pp_BillReference);
        paramMap.put("pp_Description", pp_Description);
        paramMap.put("pp_Language", pp_Language);
        paramMap.put("pp_MerchantID", pp_MerchantID);
        paramMap.put("pp_Password", pp_Password);
        paramMap.put("pp_ProductID", pp_ProductID);
        paramMap.put("pp_ReturnURL", pp_ReturnURL);
        // pp_SubMerchantID 为空，不添加
        paramMap.put("pp_TxnCurrency", pp_TxnCurrency);
        paramMap.put("pp_TxnDateTime", pp_TxnDateTime);
        paramMap.put("pp_TxnExpiryDateTime", pp_TxnExpiryDateTime);
        paramMap.put("pp_TxnRefNo", pp_TxnRefNo);
        paramMap.put("pp_TxnType", pp_TxnType); // 现在包含MPAY
        paramMap.put("pp_Version", pp_Version);
        paramMap.put("ppmpf_1", pp_mpf_1);
        paramMap.put("ppmpf_2", pp_mpf_2);
        paramMap.put("ppmpf_3", pp_mpf_3);
        paramMap.put("ppmpf_4", pp_mpf_4);
        paramMap.put("ppmpf_5", pp_mpf_5);

        // 按字母顺序构建哈希字符串
        StringBuilder sortedStringBuilder = new StringBuilder();
        sortedStringBuilder.append(IntegritySalt);

        Log.d(TAG, "=== 哈希计算参数（按字母顺序） ===");
        Log.d(TAG, "IntegritySalt: " + IntegritySalt);

        for (String key : paramMap.keySet()) {
            String value = paramMap.get(key);
            if (value != null && !value.isEmpty()) {
                sortedStringBuilder.append("&").append(value);
                Log.d(TAG, key + ": " + value);
            }
        }

        String sortedString = sortedStringBuilder.toString();
        pp_SecureHash = php_hash_hmac(sortedString, IntegritySalt);

        Log.d(TAG, "=== 哈希计算结果 ===");
        Log.d(TAG, "排序后的字符串: " + sortedString);
        Log.d(TAG, "计算的哈希值: " + pp_SecureHash);
        System.out.println("AhmadLogs: sortedString : " + sortedString);
        System.out.println("AhmadLogs: pp_SecureHash : " + pp_SecureHash);

        // 构建POST数据
        Log.d(TAG, "=== 构建POST数据 ===");
        try {
            postData += URLEncoder.encode("pp_Version", "UTF-8")
                    + "=" + URLEncoder.encode(pp_Version, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_TxnType", "UTF-8")
                    + "=" + URLEncoder.encode(pp_TxnType, "UTF-8") + "&"; // 修复：添加TxnType
            postData += URLEncoder.encode("pp_Language", "UTF-8")
                    + "=" + URLEncoder.encode(pp_Language, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_MerchantID", "UTF-8")
                    + "=" + URLEncoder.encode(pp_MerchantID, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_SubMerchantID", "UTF-8")
                    + "=" + pp_SubMerchantID + "&";
            postData += URLEncoder.encode("pp_Password", "UTF-8")
                    + "=" + URLEncoder.encode(pp_Password, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_BankID", "UTF-8")
                    + "=" + URLEncoder.encode(pp_BankID, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_ProductID", "UTF-8")
                    + "=" + URLEncoder.encode(pp_ProductID, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_TxnRefNo", "UTF-8")
                    + "=" + URLEncoder.encode(pp_TxnRefNo, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_Amount", "UTF-8")
                    + "=" + URLEncoder.encode(pp_Amount, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_TxnCurrency", "UTF-8")
                    + "=" + URLEncoder.encode(pp_TxnCurrency, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_TxnDateTime", "UTF-8")
                    + "=" + URLEncoder.encode(pp_TxnDateTime, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_BillReference", "UTF-8")
                    + "=" + URLEncoder.encode(pp_BillReference, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_Description", "UTF-8")
                    + "=" + URLEncoder.encode(pp_Description, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_TxnExpiryDateTime", "UTF-8")
                    + "=" + URLEncoder.encode(pp_TxnExpiryDateTime, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_ReturnURL", "UTF-8")
                    + "=" + URLEncoder.encode(pp_ReturnURL, "UTF-8") + "&";
            postData += URLEncoder.encode("pp_SecureHash", "UTF-8")
                    + "=" + pp_SecureHash + "&";
            postData += URLEncoder.encode("ppmpf_1", "UTF-8")
                    + "=" + URLEncoder.encode(pp_mpf_1, "UTF-8") + "&";
            postData += URLEncoder.encode("ppmpf_2", "UTF-8")
                    + "=" + URLEncoder.encode(pp_mpf_2, "UTF-8") + "&";
            postData += URLEncoder.encode("ppmpf_3", "UTF-8")
                    + "=" + URLEncoder.encode(pp_mpf_3, "UTF-8") + "&";
            postData += URLEncoder.encode("ppmpf_4", "UTF-8")
                    + "=" + URLEncoder.encode(pp_mpf_4, "UTF-8") + "&";
            postData += URLEncoder.encode("ppmpf_5", "UTF-8")
                    + "=" + URLEncoder.encode(pp_mpf_5, "UTF-8");

        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "URL编码错误", e);
            e.printStackTrace();
        }

        Log.d(TAG, "=== 发送支付请求 ===");
        Log.d(TAG, "目标URL: " + SANDBOX_URL);
        Log.d(TAG, "POST数据: " + postData);
        System.out.println("AhmadLogs: postData : " + postData);

        mWebView.postUrl(SANDBOX_URL, postData.getBytes());
        Log.d(TAG, "支付请求已发送到JazzCash");
    }

    private class MyWebViewClient extends WebViewClient {
        private final String jsCode ="" + "function parseForm(form){"+
                "var values='';"+
                "for(var i=0 ; i< form.elements.length; i++){"+
                "   values+=form.elements[i].name+'='+form.elements[i].value+'&'"+
                "}"+
                "var url=form.action;"+
                "console.log('parse form fired');"+
                "window.FORMOUT.processFormData(url,values);"+
                "   }"+
                "for(var i=0 ; i< document.forms.length ; i++){"+
                "   parseForm(document.forms[i]);"+
                "};";

        //private static final String DEBUG_TAG = "CustomWebClient";

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            Log.d(TAG, "=== 页面开始加载 ===");
            Log.d(TAG, "URL: " + url);
            if(url.equals(paymentReturnUrl)){
                Log.d(TAG, "检测到返回URL，停止加载");
                System.out.println("AhmadLogs: return url cancelling");
                view.stopLoading();
                return;
            }
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            Log.d(TAG, "=== 页面加载完成 ===");
            Log.d(TAG, "URL: " + url);
            if(url.equals(paymentReturnUrl)){
                Log.d(TAG, "返回URL页面加载完成");
                return;
            }
            Log.d(TAG, "注入JavaScript代码以解析表单");
            view.loadUrl("javascript:(function() { " + jsCode + "})()");

            super.onPageFinished(view, url);
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            Log.e(TAG, "=== WebView错误 ===");
            Log.e(TAG, "错误代码: " + errorCode);
            Log.e(TAG, "错误描述: " + description);
            Log.e(TAG, "失败URL: " + failingUrl);
            super.onReceivedError(view, errorCode, description, failingUrl);
        }
    }

    private class FormDataInterface {
        @JavascriptInterface
        public void processFormData(String url, String formData) {
            Log.d(TAG, "=== 接收到表单数据 ===");
            Log.d(TAG, "URL: " + url);
            Log.d(TAG, "表单数据: " + formData);
            System.out.println("AhmadLogs: Url:" + url + " form data " + formData);

            Intent i = new Intent(PaymentActivity.this, MainActivity.class);

            if (url.equals(paymentReturnUrl)) {
                Log.d(TAG, "=== 处理支付响应 ===");
                TreeMap<String, String> responseParams = new TreeMap<>();
                String responseHash = "";
                String responseCode = "";

                String[] values = formData.split("&");
                for (String pair : values) {
                    String[] nameValue = pair.split("=");
                    if (nameValue.length == 2) {
                        String name = nameValue[0];
                        String value = nameValue[1];
                        Log.d(TAG, name + ": " + value);
                        System.out.println("AhmadLogs: Name:" + name + " value:" + value);

                        if ("pp_SecureHash".equals(name)) {
                            responseHash = value;
                        } else if ("pp_ResponseCode".equals(name)) {
                            responseCode = value;
                        } else if (name.startsWith("pp_") && !name.equals("pp_SecureHash")) {
                            // 收集用于哈希验证的参数
                            if (!value.isEmpty()) {
                                responseParams.put(name, value);
                            }
                        }

                        i.putExtra(name, value);
                    }
                }

                // 验证响应哈希
                validateResponseHash(responseParams, responseHash, responseCode);

                setResult(RESULT_OK, i);
                finish();

                return;
            }
        }

        private void validateResponseHash(TreeMap<String, String> responseParams, String receivedHash, String responseCode) {
            Log.d(TAG, "=== 验证响应哈希 ===");
            Log.d(TAG, "响应代码: " + responseCode);
            Log.d(TAG, "接收到的哈希: " + receivedHash);

            // 构建验证哈希字符串
            StringBuilder hashStringBuilder = new StringBuilder();
            hashStringBuilder.append(Jazz_IntegritySalt);

            for (String key : responseParams.keySet()) {
                String value = responseParams.get(key);
                if (value != null && !value.isEmpty()) {
                    hashStringBuilder.append("&").append(value);
                    Log.d(TAG, "哈希参数 " + key + ": " + value);
                }
            }

            String hashString = hashStringBuilder.toString();
            String calculatedHash = php_hash_hmac(hashString, Jazz_IntegritySalt);

            Log.d(TAG, "计算哈希字符串: " + hashString);
            Log.d(TAG, "计算的哈希: " + calculatedHash);

            if (calculatedHash.equals(receivedHash)) {
                Log.d(TAG, "✅ 响应哈希验证成功");
                Toast.makeText(PaymentActivity.this, "响应哈希验证成功", Toast.LENGTH_SHORT).show();
            } else {
                Log.e(TAG, "❌ 响应哈希验证失败");
                Toast.makeText(PaymentActivity.this, "响应哈希验证失败", Toast.LENGTH_LONG).show();
            }

            // 解释响应代码
            interpretResponseCode(responseCode);
        }

        private void interpretResponseCode(String responseCode) {
            Log.d(TAG, "=== 响应代码解释 ===");
            String message = "";
            switch (responseCode) {
                case "000":
                    message = "✅ 交易成功";
                    break;
                case "FE34":
                    message = "❌ 认证失败 - 可能是哈希计算错误";
                    break;
                case "124":
                    message = "❌ 交易被拒绝";
                    break;
                case "101":
                    message = "❌ 商户认证失败";
                    break;
                default:
                    message = "❓ 未知响应代码: " + responseCode;
                    break;
            }
            Log.d(TAG, message);
            Toast.makeText(PaymentActivity.this, message, Toast.LENGTH_LONG).show();
        }
    }

    public static String php_hash_hmac(String data, String secret) {
        String returnString = "";
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] res = sha256_HMAC.doFinal(data.getBytes());
            returnString = bytesToHex(res);

        }catch (Exception e){
            e.printStackTrace();
        }

        return returnString;
    }

    public static String bytesToHex(byte[] bytes) {
        final char[] hexArray = "0123456789abcdef".toCharArray();
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0, v; j < bytes.length; j++) {
            v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }
}