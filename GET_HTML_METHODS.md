# 获取TransactionProcessing页面HTML的方法

## 方法1：通过WebView JavaScript获取（已实现）

我已经在您的代码中添加了获取HTML的功能：

### 实现原理
1. **检测TransactionProcessing页面**：在`onPageFinished`中检查URL是否包含"TransactionProcessing"
2. **注入JavaScript代码**：获取页面的完整HTML内容
3. **回调到Android**：通过`receiveHTML`方法接收HTML数据

### 使用方法
运行应用后查看Logcat：
```bash
adb logcat -s JazzCashPayment
```

期望看到的日志：
```
D/JazzCashPayment: 检测到TransactionProcessing页面，获取HTML内容
D/JazzCashPayment: === 接收到页面HTML ===
D/JazzCashPayment: 页面URL: https://sandbox.jazzcash.com.pk/CustomerPortal/TransactionManagement/TransactionProcessing?Identifier=xxx
D/JazzCashPayment: HTML长度: 1234
D/JazzCashPayment: HTML内容: <!DOCTYPE html>...
```

## 方法2：Chrome DevTools调试（推荐用于开发调试）

### 启用WebView调试
在您的代码中添加（如果还没有）：
```java
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
    WebView.setWebContentsDebuggingEnabled(true);
}
```

### 使用步骤
1. **连接设备**：确保设备通过USB连接到电脑
2. **启用USB调试**：在设备的开发者选项中启用
3. **打开Chrome**：在电脑上打开Chrome浏览器
4. **访问调试页面**：在地址栏输入 `chrome://inspect`
5. **选择WebView**：找到您的应用的WebView并点击"inspect"
6. **查看源码**：在DevTools中可以查看完整的HTML源码

### 优势
- 实时查看页面结构
- 可以检查CSS样式
- 可以执行JavaScript命令
- 可以查看网络请求

## 方法3：网络抓包（用于分析完整请求响应）

### 使用Charles Proxy或Fiddler
1. **设置代理**：在设备上设置HTTP代理
2. **安装证书**：安装抓包工具的证书以支持HTTPS
3. **启动抓包**：开始监控网络请求
4. **执行支付**：触发支付流程
5. **查看响应**：在抓包工具中查看TransactionProcessing页面的响应内容

### 使用ADB命令
```bash
# 启用网络日志
adb shell setprop log.tag.chromium_network VERBOSE
adb logcat -s chromium_network
```

## 方法4：保存页面到文件（代码实现）

可以修改`receiveHTML`方法来保存HTML到文件：

```java
@JavascriptInterface
public void receiveHTML(String url, String html) {
    Log.d(TAG, "=== 接收到页面HTML ===");
    Log.d(TAG, "页面URL: " + url);
    Log.d(TAG, "HTML长度: " + html.length());
    
    // 保存HTML到文件
    try {
        String fileName = "transaction_processing_" + System.currentTimeMillis() + ".html";
        File file = new File(getExternalFilesDir(null), fileName);
        FileWriter writer = new FileWriter(file);
        writer.write(html);
        writer.close();
        Log.d(TAG, "HTML已保存到文件: " + file.getAbsolutePath());
    } catch (IOException e) {
        Log.e(TAG, "保存HTML文件失败", e);
    }
}
```

## 方法5：使用OkHttp直接请求（模拟请求）

如果您想直接获取页面内容而不通过WebView：

```java
// 需要添加OkHttp依赖
OkHttpClient client = new OkHttpClient();

// 构建POST请求（使用相同的参数）
RequestBody formBody = new FormBody.Builder()
    .add("pp_Version", "1.1")
    .add("pp_MerchantID", "您的商户ID")
    // ... 其他参数
    .build();

Request request = new Request.Builder()
    .url("https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform/")
    .post(formBody)
    .build();

client.newCall(request).enqueue(new Callback() {
    @Override
    public void onResponse(Call call, Response response) throws IOException {
        String html = response.body().string();
        Log.d(TAG, "直接请求获取的HTML: " + html);
    }
    
    @Override
    public void onFailure(Call call, IOException e) {
        Log.e(TAG, "请求失败", e);
    }
});
```

## 推荐使用顺序

1. **开发阶段**：使用Chrome DevTools（方法2）进行实时调试
2. **代码集成**：使用WebView JavaScript获取（方法1）
3. **深度分析**：使用网络抓包（方法3）了解完整的请求响应流程
4. **自动化测试**：使用直接HTTP请求（方法5）

## 注意事项

1. **HTML内容可能很长**：Logcat有长度限制，可能需要保存到文件
2. **动态内容**：页面可能包含动态生成的JavaScript，需要等待执行完成
3. **时机问题**：确保在页面完全加载后再获取HTML
4. **权限问题**：保存文件可能需要存储权限

## 当前实现的优势

- **无需额外权限**：直接通过WebView获取
- **实时获取**：在页面加载完成后立即获取
- **完整内容**：获取的是渲染后的完整HTML
- **简单集成**：只需要几行JavaScript代码

现在您可以运行应用，当访问TransactionProcessing页面时，就能在Logcat中看到完整的HTML内容了。
