# JazzCash集成测试指南

## 测试改进后的功能

### 1. 编译和运行测试

```bash
# 清理项目
./gradlew clean

# 编译项目
./gradlew build

# 安装到设备
./gradlew installDebug

# 查看日志
adb logcat -s JazzCashPayment
```

### 2. 功能测试清单

#### ✅ 基本支付流程
- [ ] 应用启动正常
- [ ] 点击购买按钮
- [ ] WebView加载JazzCash页面
- [ ] 查看详细日志输出

#### ✅ 哈希计算验证
检查日志中的哈希计算过程：
```
=== 哈希计算参数（按字母顺序） ===
IntegritySalt: 8zzx29y512
pp_Amount: 27500
pp_BankID: TBANK
pp_BillReference: billRef
pp_Description: Description of transaction
pp_Language: EN
pp_MerchantID: MC158532
pp_Password: 0b64sxf81t
pp_ProductID: RETL
pp_ReturnURL: https://app-api.eswap.com/swap/pay/jazzcash/notify
pp_TxnCurrency: PKR
pp_TxnDateTime: **************
pp_TxnExpiryDateTime: **************
pp_TxnRefNo: T**************
pp_TxnType: MPAY
pp_Version: 1.1
ppmpf_1: 1
ppmpf_2: 2
ppmpf_3: 3
ppmpf_4: 4
ppmpf_5: 5
```

#### ✅ 下拉刷新功能
- [ ] 在WebView中向下滑动
- [ ] 看到刷新指示器
- [ ] 显示"重新发起支付请求..."提示
- [ ] 生成新的交易ID和时间戳
- [ ] 重新计算哈希值

#### ✅ 响应处理
- [ ] 接收JazzCash响应
- [ ] 验证响应哈希
- [ ] 显示响应代码解释
- [ ] 正确处理成功/失败情况

### 3. 预期的日志输出示例

```
D/JazzCashPayment: === 开始JazzCash支付流程 ===
D/JazzCashPayment: 原始价格: 275.00
D/JazzCashPayment: 处理后的价格: 27500
D/JazzCashPayment: 交易日期时间: **************
D/JazzCashPayment: 交易过期时间: **************
D/JazzCashPayment: 交易ID: T**************
D/JazzCashPayment: === 支付参数详情 ===
D/JazzCashPayment: pp_MerchantID: MC158532
D/JazzCashPayment: pp_Password: 0b64sxf81t
D/JazzCashPayment: pp_Amount: 27500
D/JazzCashPayment: pp_TxnType: MPAY
D/JazzCashPayment: === 哈希计算参数（按字母顺序） ===
D/JazzCashPayment: 排序后的字符串: 8zzx29y512&27500&TBANK&billRef&Description of transaction&EN&MC158532&0b64sxf81t&RETL&https://app-api.eswap.com/swap/pay/jazzcash/notify&PKR&**************&**************&T**************&MPAY&1.1&1&2&3&4&5
D/JazzCashPayment: 计算的哈希值: [新的哈希值]
D/JazzCashPayment: === 发送支付请求 ===
D/JazzCashPayment: 目标URL: https://sandbox.jazzcash.com.pk/CustomerPortal/transactionmanagement/merchantform/
D/JazzCashPayment: 支付请求已发送到JazzCash
```

### 4. 对比分析

#### 修复前的问题
```
pp_TxnType=""  // 空值
sortedString: 8zzx29y512&27500&TBANK&billRef&Description of transaction&EN&MC158532&0b64sxf81t&RETL&https://app-api.eswap.com/swap/pay/jazzcash/notify&PKR&**************&**************&T**************&1.1&1&2&3&4&5
// 缺少MPAY，参数顺序可能不正确
```

#### 修复后的改进
```
pp_TxnType: MPAY  // 正确设置
sortedString: 8zzx29y512&27500&TBANK&billRef&Description of transaction&EN&MC158532&0b64sxf81t&RETL&https://app-api.eswap.com/swap/pay/jazzcash/notify&PKR&**************&**************&T**************&MPAY&1.1&1&2&3&4&5
// 包含MPAY，严格按字母顺序排列
```

### 5. 故障排除

#### 如果仍然收到FE34错误
1. 检查商户凭据是否正确
2. 验证哈希计算字符串的格式
3. 确认所有参数都按字母顺序排列
4. 检查IntegritySalt是否正确

#### 如果下拉刷新不工作
1. 确认SwipeRefreshLayout依赖已添加
2. 检查布局文件是否正确更新
3. 验证findViewById调用是否成功

#### 如果日志不显示
1. 确认日志标签"JazzCashPayment"
2. 检查Log.d调用是否正确
3. 验证设备日志级别设置

### 6. 成功指标

- ✅ 不再出现FE34错误
- ✅ 哈希计算过程清晰可见
- ✅ 下拉刷新功能正常工作
- ✅ 响应验证成功
- ✅ 用户体验改善

### 7. 下一步优化建议

1. 添加网络状态检查
2. 实现重试机制
3. 添加支付状态持久化
4. 优化错误处理流程
5. 添加单元测试
